#include "audio_codec.h"
#include "board.h"
#include "settings.h"

#include <esp_log.h>
#include <cstring>
#include <driver/i2s_common.h>

#define TAG "AudioCodec"

AudioCodec::AudioCodec() {
}

AudioCodec::~AudioCodec() {
}
//注册输入输出回调函数，当有新的音频输入数据准备好可以处理时，系统会调用这个回调函数。
void AudioCodec::OnInputReady(std::function<bool()> callback) {
    on_input_ready_ = callback;
}

void AudioCodec::OnOutputReady(std::function<bool()> callback) {
    on_output_ready_ = callback;
}
//将音频数据写入输出缓冲区
void AudioCodec::OutputData(std::vector<int16_t>& data) {
    Write(data.data(), data.size());
}
//从输入缓冲区读取音频数据
bool AudioCodec::InputData(std::vector<int16_t>& data) {
    //计算 30ms 音频数据的采样点数
    int duration = 30;
    //采样点数=采样率×持续时间×通道数
    int input_frame_size = input_sample_rate_ / 1000 * duration * input_channels_;
    //将vector调整为计算得到的采样点数
    data.resize(input_frame_size);
    //实际读取的采样点数，第一个参数为获取vector的原始数据指针，第二个参数为传递vector的大小
    int samples = Read(data.data(), data.size());
    //判断采样点数是否大于0，大于0则读取成功，反则读取失败
    if (samples > 0) {
        return true;
    }
    return false;
}
//检查输出是否已启用，检查是否已注册输出就绪回调函数。若两个条件均满足，调用用户注册的回调函数，并返回执行结果
//填充新的音频数据到输出缓冲区，确保音频流的连续性。
IRAM_ATTR bool AudioCodec::on_sent(i2s_chan_handle_t handle, i2s_event_data_t *event, void *user_ctx) {
                              //I2S通道句柄，标识触发回调的具体 I2S 通道
                              //I2S 事件数据结构体，包含事件类型（如数据发送完成）和相关参数。
                              //用户上下文指针，在注册回调时传入（通常是this指针），用于访问类成员。
    auto audio_codec = (AudioCodec*)user_ctx;
    if (audio_codec->output_enabled_ && audio_codec->on_output_ready_) {
        return audio_codec->on_output_ready_();
    }
    return false;
}
//检查输入是否已启用，检查是否已注册输入就绪回调函数。若两个条件均满足，调用用户注册的回调函数，并返回执行结果
//从输入缓冲区读取音频数据并进行处理
IRAM_ATTR bool AudioCodec::on_recv(i2s_chan_handle_t handle, i2s_event_data_t *event, void *user_ctx) {
    auto audio_codec = (AudioCodec*)user_ctx;
    if (audio_codec->input_enabled_ && audio_codec->on_input_ready_) {
        return audio_codec->on_input_ready_();
    }
    return false;
}

void AudioCodec::Start() {
    //从配置文件加载音频相关设置。
    Settings settings("audio", false);
    //获取存储的音量值，若不存在则使用默认值output_volume_。
    output_volume_ = settings.GetInt("output_volume", output_volume_);

    // 注册音频数据回调
    //on_recv：当 I2S 接收到新数据时触发。
    //on_sent：当 I2S 发送完数据时触发。
    //注册接入回调
    //i2s_event_callbacks_t是ESP32 I2S 驱动的回调结构体，包含不同事件类型的处理函数指针。
    i2s_event_callbacks_t rx_callbacks = {};
    rx_callbacks.on_recv = on_recv;
    i2s_channel_register_event_callback(rx_handle_, &rx_callbacks, this);
    //注册发送回调
    i2s_event_callbacks_t tx_callbacks = {};
    tx_callbacks.on_sent = on_sent;
    i2s_channel_register_event_callback(tx_handle_, &tx_callbacks, this);
    //启用I2S通道，开始数据传输
    //ESP_ERROR_CHECK是ESP-IDF 的错误检查宏，若函数返回非零错误码则终止程序并输出错误信息。
    ESP_ERROR_CHECK(i2s_channel_enable(tx_handle_));//发送通道句柄
    ESP_ERROR_CHECK(i2s_channel_enable(rx_handle_));//接受通道句柄

    EnableInput(true);//允许从麦克风等输入设备采集音频。
    EnableOutput(true);//允许向扬声器等输出设备播放音频。
}
//调整音频输出的音量大小，并将新设置保存在配置文件中。这使得音量设置在设备重启后仍能保持
void AudioCodec::SetOutputVolume(int volume) {
    output_volume_ = volume;//更新内存中的音量状态
    ESP_LOGI(TAG, "Set output volume to %d", output_volume_);//调试时可追踪音量变化。
    
    Settings settings("audio", true);//打开音频相关的配置存储。
    settings.SetInt("output_volume", output_volume_);//持久化保存音量设置，确保重启后仍有效。
}
//控制音频输入通道（如麦克风）的开启与关闭。
void AudioCodec::EnableInput(bool enable) {
    if (enable == input_enabled_) {
        return;
    }
    input_enabled_ = enable;
    ESP_LOGI(TAG, "Set input enable to %s", enable ? "true" : "false");
}
//控制音频输出通道（如扬声器）的开启与关闭。
void AudioCodec::EnableOutput(bool enable) {
    if (enable == output_enabled_) {
        return;
    }
    output_enabled_ = enable;
    ESP_LOGI(TAG, "Set output enable to %s", enable ? "true" : "false");
}
